require('dotenv').config();
const { AgentRPC } = require('agentrpc');
const { z } = require('zod');

// Initialize AgentRPC
const rpc = new AgentRPC({
  apiSecret: process.env.AGENTRPC_API_SECRET,
});

console.log('🧪 Testing minimal tool...');

// Test with the most basic schema possible
rpc.register({
  name: 'testMinimal',
  description: 'Minimal test tool',
  schema: z.object({
    message: z.string()
  }),
  handler: async ({ message }) => {
    return { success: true, message };
  }
});

console.log('✅ Registered minimal test tool');
rpc.listen();
console.log('🎯 Minimal test tool is ready!');
