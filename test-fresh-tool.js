require('dotenv').config();
const { AgentRPC } = require('agentrpc');
const { z } = require('zod');

// Initialize AgentRPC
const rpc = new AgentRPC({
  apiSecret: process.env.AGENTRPC_API_SECRET,
});

console.log('🧪 Testing fresh tool registration...');

// Register a completely fresh tool with unique name
rpc.register({
  name: 'freshTestTool2024',
  description: 'Fresh test tool for debugging',
  schema: z.object({
    input: z.string().describe('Test input string')
  }),
  handler: async ({ input }) => {
    return {
      success: true,
      input,
      timestamp: new Date().toISOString(),
      message: 'Fresh tool working!'
    };
  }
});

console.log('✅ Registered fresh test tool');
console.log('🔗 Starting AgentRPC server...');

// Start listening
rpc.listen();

console.log('🎯 Fresh test tool is ready!');

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down fresh test server...');
  process.exit(0);
});
