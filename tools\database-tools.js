const { z } = require('zod');

module.exports = function(rpc) {
  const tools = [];

  // MySQL database operations
  rpc.register({
    name: 'queryMysqlDatabase',
    description: 'Execute SQL query on MySQL database',
    schema: z.object({
      query: z.string().describe('SQL query to execute'),
      params: z.array(z.any()).optional().describe('Query parameters for prepared statements')
    }),
    handler: async ({ query, params = [] }) => {
      try {
        const mysql = require('mysql2/promise');
        
        const connection = await mysql.createConnection({
          host: process.env.MYSQL_HOST,
          user: process.env.MYSQL_USER,
          password: process.env.MYSQL_PASSWORD,
          database: process.env.MYSQL_DATABASE
        });
        
        const [rows, fields] = await connection.execute(query, params);
        await connection.end();
        
        return {
          success: true,
          query,
          rowCount: Array.isArray(rows) ? rows.length : rows.affectedRows,
          data: rows,
          fields: fields?.map(f => f.name) || []
        };
      } catch (error) {
        throw new Error(`MySQL query failed: ${error.message}`);
      }
    }
  });
  tools.push('queryMysqlDatabase');

  // Enhanced MongoDB Operations
  rpc.register({
    name: 'queryDatabase',
    description: 'Execute MongoDB operations including reads, writes, and admin operations',
    schema: z.object({
      operation: z.enum(['find', 'findOne', 'insertOne', 'insertMany', 'updateOne', 'updateMany', 'deleteOne', 'deleteMany', 'aggregate', 'count', 'listCollections', 'listDatabases', 'createCollection', 'dropCollection', 'createIndex', 'dbStats']).describe('MongoDB operation to perform'),
      database: z.string().optional().describe('Database name (optional, will use connection default if not provided)'),
      collection: z.string().optional().describe('Collection name (required for most operations)'),
      query: z.any().optional().describe('Query criteria as JSON object'),
      document: z.any().optional().describe('Document(s) to insert/update as JSON object'),
      update: z.any().optional().describe('Update operations as JSON object'),
      options: z.any().optional().describe('Additional options as JSON object'),
      pipeline: z.array(z.any()).optional().describe('Aggregation pipeline array'),
      indexSpec: z.any().optional().describe('Index specification for createIndex'),
      useEnvConnection: z.boolean().default(true).describe('Use connection string from .env file')
    }),
    handler: async ({ operation, database, collection, query = {}, document, update, options = {}, pipeline, indexSpec, useEnvConnection = true }) => {
      try {
        const { MongoClient } = require('mongodb');
        
        // Use connection string from environment or default
        const connectionString = useEnvConnection ? process.env.MONGODB_URI : 
          'mongodb+srv://nick_the_great:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
        
        if (!connectionString) {
          throw new Error('MongoDB connection string not found in environment variables');
        }

        const client = new MongoClient(connectionString);
        await client.connect();
        
        let db;
        if (database) {
          db = client.db(database);
        } else {
          // Use default database from connection string or first available
          db = client.db();
        }
        
        let results;
        let operationDetails = {
          operation,
          database: database || 'default',
          collection,
          executedAt: new Date().toISOString()
        };

        switch (operation) {
          case 'find':
            if (!collection) throw new Error('Collection name required for find operation');
            results = await db.collection(collection).find(query, options).toArray();
            operationDetails.query = query;
            operationDetails.resultCount = results.length;
            break;

          case 'findOne':
            if (!collection) throw new Error('Collection name required for findOne operation');
            results = await db.collection(collection).findOne(query, options);
            operationDetails.query = query;
            operationDetails.found = results !== null;
            break;

          case 'insertOne':
            if (!collection) throw new Error('Collection name required for insertOne operation');
            if (!document) throw new Error('Document required for insertOne operation');
            results = await db.collection(collection).insertOne(document, options);
            operationDetails.insertedId = results.insertedId;
            operationDetails.acknowledged = results.acknowledged;
            break;

          case 'insertMany':
            if (!collection) throw new Error('Collection name required for insertMany operation');
            if (!document || !Array.isArray(document)) throw new Error('Array of documents required for insertMany operation');
            results = await db.collection(collection).insertMany(document, options);
            operationDetails.insertedCount = results.insertedCount;
            operationDetails.insertedIds = results.insertedIds;
            operationDetails.acknowledged = results.acknowledged;
            break;

          case 'updateOne':
            if (!collection) throw new Error('Collection name required for updateOne operation');
            if (!update) throw new Error('Update document required for updateOne operation');
            results = await db.collection(collection).updateOne(query, update, options);
            operationDetails.query = query;
            operationDetails.update = update;
            operationDetails.matchedCount = results.matchedCount;
            operationDetails.modifiedCount = results.modifiedCount;
            operationDetails.acknowledged = results.acknowledged;
            break;

          case 'updateMany':
            if (!collection) throw new Error('Collection name required for updateMany operation');
            if (!update) throw new Error('Update document required for updateMany operation');
            results = await db.collection(collection).updateMany(query, update, options);
            operationDetails.query = query;
            operationDetails.update = update;
            operationDetails.matchedCount = results.matchedCount;
            operationDetails.modifiedCount = results.modifiedCount;
            operationDetails.acknowledged = results.acknowledged;
            break;

          case 'deleteOne':
            if (!collection) throw new Error('Collection name required for deleteOne operation');
            results = await db.collection(collection).deleteOne(query, options);
            operationDetails.query = query;
            operationDetails.deletedCount = results.deletedCount;
            operationDetails.acknowledged = results.acknowledged;
            break;

          case 'deleteMany':
            if (!collection) throw new Error('Collection name required for deleteMany operation');
            results = await db.collection(collection).deleteMany(query, options);
            operationDetails.query = query;
            operationDetails.deletedCount = results.deletedCount;
            operationDetails.acknowledged = results.acknowledged;
            break;

          case 'aggregate':
            if (!collection) throw new Error('Collection name required for aggregate operation');
            if (!pipeline || !Array.isArray(pipeline)) throw new Error('Pipeline array required for aggregate operation');
            results = await db.collection(collection).aggregate(pipeline, options).toArray();
            operationDetails.pipeline = pipeline;
            operationDetails.resultCount = results.length;
            break;

          case 'count':
            if (!collection) throw new Error('Collection name required for count operation');
            results = await db.collection(collection).countDocuments(query, options);
            operationDetails.query = query;
            operationDetails.count = results;
            break;

          case 'listCollections':
            results = await db.listCollections().toArray();
            operationDetails.collectionCount = results.length;
            break;

          case 'listDatabases':
            results = await client.db().admin().listDatabases();
            operationDetails.databaseCount = results.databases.length;
            break;

          case 'createCollection':
            if (!collection) throw new Error('Collection name required for createCollection operation');
            results = await db.createCollection(collection, options);
            operationDetails.collectionCreated = collection;
            break;

          case 'dropCollection':
            if (!collection) throw new Error('Collection name required for dropCollection operation');
            results = await db.collection(collection).drop();
            operationDetails.collectionDropped = collection;
            operationDetails.dropped = results;
            break;

          case 'createIndex':
            if (!collection) throw new Error('Collection name required for createIndex operation');
            if (!indexSpec) throw new Error('Index specification required for createIndex operation');
            results = await db.collection(collection).createIndex(indexSpec, options);
            operationDetails.indexSpec = indexSpec;
            operationDetails.indexName = results;
            break;

          case 'dbStats':
            results = await db.stats();
            operationDetails.databaseName = results.db;
            break;

          default:
            throw new Error(`Unsupported operation: ${operation}`);
        }

        await client.close();

        return {
          success: true,
          operation: operationDetails,
          results,
          connectionInfo: {
            connected: true,
            server: connectionString.includes('mongodb.net') ? 'MongoDB Atlas' : 'Local MongoDB',
            database: operationDetails.database
          }
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          operation,
          database,
          collection,
          executedAt: new Date().toISOString()
        };
      }
    }
  });
  tools.push('queryDatabase');

  // PostgreSQL operations
  rpc.register({
    name: 'queryPostgresDatabase',
    description: 'Execute SQL query on PostgreSQL database',
    schema: z.object({
      query: z.string().describe('SQL query to execute'),
      params: z.array(z.any()).optional().describe('Query parameters')
    }),
    handler: async ({ query, params = [] }) => {
      try {
        const { Client } = require('pg');
        
        const client = new Client({
          host: process.env.POSTGRES_HOST,
          user: process.env.POSTGRES_USER,
          password: process.env.POSTGRES_PASSWORD,
          database: process.env.POSTGRES_DATABASE
        });
        
        await client.connect();
        const result = await client.query(query, params);
        await client.end();
        
        return {
          success: true,
          query,
          rowCount: result.rowCount,
          rows: result.rows,
          fields: result.fields?.map(f => f.name) || []
        };
      } catch (error) {
        throw new Error(`PostgreSQL query failed: ${error.message}`);
      }
    }
  });
  tools.push('queryPostgresDatabase');

  // Database backup
  rpc.register({
    name: 'backupDatabase',
    description: 'Create a backup of database',
    schema: z.object({
      dbType: z.enum(['mysql', 'postgres', 'mongodb']).describe('Database type'),
      backupPath: z.string().describe('Path to save backup file'),
      tables: z.array(z.string()).optional().describe('Specific tables/collections to backup')
    }),
    handler: async ({ dbType, backupPath, tables }) => {
      // This would implement actual backup logic using mysqldump, pg_dump, mongodump etc.
      return {
        success: true,
        dbType,
        backupPath,
        tables: tables || 'all',
        backupSize: '0 MB',
        note: 'Database backup implementation needed',
        timestamp: new Date().toISOString()
      };
    }
  });
  tools.push('backupDatabase');



  // Cloud Storage - Upload File (AWS S3 & Google Cloud Storage)
  rpc.register({
    name: 'uploadFileToCloud',
    description: 'Upload a file to cloud storage (AWS S3 or Google Cloud Storage)',
    schema: z.object({
      provider: z.enum(['s3', 'gcs']).describe('Cloud storage provider (s3 for AWS S3, gcs for Google Cloud Storage)'),
      localFilePath: z.string().describe('Local path to the file to upload'),
      cloudFileName: z.string().optional().describe('Name for the file in cloud storage (defaults to original filename)'),
      bucketName: z.string().describe('Name of the bucket/container'),
      folder: z.string().optional().describe('Folder/prefix within the bucket (optional)'),
      makePublic: z.boolean().default(false).describe('Make the uploaded file publicly accessible'),
      metadata: z.any().optional().describe('Additional metadata to attach to the file'),
      overwrite: z.boolean().default(true).describe('Overwrite if file already exists'),
      storageClass: z.string().optional().describe('Storage class (e.g., STANDARD, GLACIER for S3; STANDARD, COLDLINE for GCS)')
    }),
    handler: async ({ provider, localFilePath, cloudFileName, bucketName, folder, makePublic = false, metadata = {}, overwrite = true, storageClass }) => {
      try {
        const fs = require('fs-extra');
        const path = require('path');
        
        // Check if local file exists
        if (!await fs.pathExists(localFilePath)) {
          throw new Error(`Local file not found: ${localFilePath}`);
        }
        
        const fileStats = await fs.stat(localFilePath);
        const fileName = cloudFileName || path.basename(localFilePath);
        const fullCloudPath = folder ? `${folder}/${fileName}` : fileName;
        
        let result;
        
        if (provider === 's3') {
          // AWS S3 Upload
          const { S3Client, PutObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
          
          const s3Client = new S3Client({
            region: process.env.AWS_REGION || 'us-east-1',
            credentials: {
              accessKeyId: process.env.AWS_ACCESS_KEY_ID,
              secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
            }
          });
          
          // Check if file already exists
          if (!overwrite) {
            try {
              await s3Client.send(new HeadObjectCommand({
                Bucket: bucketName,
                Key: fullCloudPath
              }));
              throw new Error(`File already exists: ${fullCloudPath}`);
            } catch (error) {
              if (error.name !== 'NotFound') {
                throw error;
              }
            }
          }
          
          const fileBuffer = await fs.readFile(localFilePath);
          
          const uploadParams = {
            Bucket: bucketName,
            Key: fullCloudPath,
            Body: fileBuffer,
            Metadata: metadata,
            ...(storageClass && { StorageClass: storageClass }),
            ...(makePublic && { ACL: 'public-read' })
          };
          
          const command = new PutObjectCommand(uploadParams);
          const s3Result = await s3Client.send(command);
          
          result = {
            provider: 's3',
            bucket: bucketName,
            key: fullCloudPath,
            etag: s3Result.ETag,
            location: `https://${bucketName}.s3.${process.env.AWS_REGION || 'us-east-1'}.amazonaws.com/${fullCloudPath}`,
            size: fileStats.size,
            storageClass: storageClass || 'STANDARD',
            public: makePublic
          };
          
        } else if (provider === 'gcs') {
          // Google Cloud Storage Upload
          const { Storage } = require('@google-cloud/storage');
          
          const storage = new Storage({
            projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
            keyFilename: process.env.GOOGLE_CLOUD_KEY_FILE
          });
          
          const bucket = storage.bucket(bucketName);
          const file = bucket.file(fullCloudPath);
          
          // Check if file already exists
          if (!overwrite) {
            const [exists] = await file.exists();
            if (exists) {
              throw new Error(`File already exists: ${fullCloudPath}`);
            }
          }
          
          const uploadOptions = {
            metadata: {
              metadata: metadata,
              ...(storageClass && { storageClass: storageClass })
            },
            resumable: fileStats.size > 5 * 1024 * 1024 // Use resumable upload for files > 5MB
          };
          
          await file.save(await fs.readFile(localFilePath), uploadOptions);
          
          // Make public if requested
          if (makePublic) {
            await file.makePublic();
          }
          
          const [fileMetadata] = await file.getMetadata();
          
          result = {
            provider: 'gcs',
            bucket: bucketName,
            name: fullCloudPath,
            id: fileMetadata.id,
            location: `https://storage.googleapis.com/${bucketName}/${fullCloudPath}`,
            size: fileStats.size,
            storageClass: storageClass || 'STANDARD',
            public: makePublic,
            gsUrl: `gs://${bucketName}/${fullCloudPath}`
          };
        }
        
        return {
          success: true,
          uploaded: result,
          localFile: {
            path: localFilePath,
            size: fileStats.size,
            lastModified: fileStats.mtime.toISOString()
          },
          uploadedAt: new Date().toISOString()
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message,
          provider,
          localFilePath,
          bucketName,
          uploadedAt: new Date().toISOString()
        };
      }
    }
  });
  tools.push('uploadFileToCloud');

  // Cloud Storage - Download File (AWS S3 & Google Cloud Storage)
  rpc.register({
    name: 'downloadFileFromCloud',
    description: 'Download a file from cloud storage (AWS S3 or Google Cloud Storage)',
    schema: z.object({
      provider: z.enum(['s3', 'gcs']).describe('Cloud storage provider (s3 for AWS S3, gcs for Google Cloud Storage)'),
      bucketName: z.string().describe('Name of the bucket/container'),
      cloudFilePath: z.string().describe('Path to the file in cloud storage'),
      localFilePath: z.string().describe('Local path where to save the downloaded file'),
      createDirectories: z.boolean().default(true).describe('Create directories if they don\'t exist'),
      overwrite: z.boolean().default(true).describe('Overwrite local file if it exists'),
      getMetadata: z.boolean().default(true).describe('Retrieve file metadata along with download')
    }),
    handler: async ({ provider, bucketName, cloudFilePath, localFilePath, createDirectories = true, overwrite = true, getMetadata = true }) => {
      try {
        const fs = require('fs-extra');
        const path = require('path');
        
        // Check if local file exists and handle overwrite
        if (!overwrite && await fs.pathExists(localFilePath)) {
          throw new Error(`Local file already exists: ${localFilePath}`);
        }
        
        // Create directories if needed
        if (createDirectories) {
          await fs.ensureDir(path.dirname(localFilePath));
        }
        
        let result;
        let fileMetadata = {};
        
        if (provider === 's3') {
          // AWS S3 Download
          const { S3Client, GetObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
          
          const s3Client = new S3Client({
            region: process.env.AWS_REGION || 'us-east-1',
            credentials: {
              accessKeyId: process.env.AWS_ACCESS_KEY_ID,
              secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
            }
          });
          
          // Get metadata if requested
          if (getMetadata) {
            const headCommand = new HeadObjectCommand({
              Bucket: bucketName,
              Key: cloudFilePath
            });
            const headResult = await s3Client.send(headCommand);
            
            fileMetadata = {
              size: headResult.ContentLength,
              lastModified: headResult.LastModified,
              etag: headResult.ETag,
              contentType: headResult.ContentType,
              storageClass: headResult.StorageClass,
              metadata: headResult.Metadata || {}
            };
          }
          
          // Download file
          const getCommand = new GetObjectCommand({
            Bucket: bucketName,
            Key: cloudFilePath
          });
          
          const s3Result = await s3Client.send(getCommand);
          
          // Convert stream to buffer and save
          const chunks = [];
          for await (const chunk of s3Result.Body) {
            chunks.push(chunk);
          }
          const fileBuffer = Buffer.concat(chunks);
          await fs.writeFile(localFilePath, fileBuffer);
          
          result = {
            provider: 's3',
            bucket: bucketName,
            key: cloudFilePath,
            downloadedBytes: fileBuffer.length
          };
          
        } else if (provider === 'gcs') {
          // Google Cloud Storage Download
          const { Storage } = require('@google-cloud/storage');
          
          const storage = new Storage({
            projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
            keyFilename: process.env.GOOGLE_CLOUD_KEY_FILE
          });
          
          const bucket = storage.bucket(bucketName);
          const file = bucket.file(cloudFilePath);
          
          // Check if file exists
          const [exists] = await file.exists();
          if (!exists) {
            throw new Error(`File not found in cloud storage: ${cloudFilePath}`);
          }
          
          // Get metadata if requested
          if (getMetadata) {
            const [gcsMetadata] = await file.getMetadata();
            
            fileMetadata = {
              size: parseInt(gcsMetadata.size),
              lastModified: gcsMetadata.updated,
              etag: gcsMetadata.etag,
              contentType: gcsMetadata.contentType,
              storageClass: gcsMetadata.storageClass,
              metadata: gcsMetadata.metadata || {}
            };
          }
          
          // Download file
          await file.download({ destination: localFilePath });
          
          result = {
            provider: 'gcs',
            bucket: bucketName,
            name: cloudFilePath,
            gsUrl: `gs://${bucketName}/${cloudFilePath}`
          };
        }
        
        // Get local file stats after download
        const localStats = await fs.stat(localFilePath);
        
        return {
          success: true,
          downloaded: result,
          localFile: {
            path: localFilePath,
            size: localStats.size,
            created: localStats.birthtime.toISOString(),
            lastModified: localStats.mtime.toISOString()
          },
          cloudMetadata: getMetadata ? fileMetadata : null,
          downloadedAt: new Date().toISOString()
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message,
          provider,
          bucketName,
          cloudFilePath,
          localFilePath,
          downloadedAt: new Date().toISOString()
        };
      }
    }
  });
  tools.push('downloadFileFromCloud');

  return tools;
};
