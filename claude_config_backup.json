{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Desktop", "C:\\Users\\<USER>\\Downloads", "C:\\Users\\<USER>\\Documents", "C:\\Users\\<USER>\\AppData"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "agentql": {"command": "npx", "args": ["-y", "agentql-mcp"], "env": {"AGENTQL_API_KEY": "8ZNb_0CUbwD7lhfaugDF4TVFyfbjyC9C9e4G5r7GtOi1vk3gm6g"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "google-maps": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-google-maps"], "env": {"GOOGLE_MAPS_API_KEY": "AIzaSyASNmi3BzMG5eEBEMy1M2mqWlIM-F3s15o"}}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "C:/Users/<USER>/Desktop"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "agentrpc": {"command": "npx", "args": ["-y", "agentrpc", "mcp"], "env": {"AGENTRPC_API_SECRET": "sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799"}}}}