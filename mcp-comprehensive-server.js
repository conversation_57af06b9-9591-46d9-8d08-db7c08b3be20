#!/usr/bin/env node

require('dotenv').config();
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { zodToJsonSchema } = require('zod-to-json-schema');

// Create MCP Server
const server = new Server(
  {
    name: 'comprehensive-toolkit',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

console.error('🚀 Starting MCP Comprehensive Toolkit...');
console.error('📦 Loading tool modules...');

// Import all tool modules and convert them to MCP format
const toolModules = [
  './tools/basic-tools',
  './tools/email-tools',
  './tools/calendar-tools',
  './tools/file-tools',
  './tools/document-tools',
  './tools/development-tools',
  './tools/database-tools',
  './tools/finance-tools',
  './tools/analytics-tools',
  './tools/monitoring-tools',
  './tools/media-tools',
  './tools/location-tools',
  './tools/rest-api-tools'
];

let totalTools = 0;

// Mock RPC object to collect tool definitions
const mockRpc = {
  register: (toolDef) => {
    try {
      // Convert AgentRPC tool definition to MCP format
      const inputSchema = zodToJsonSchema(toolDef.schema, {
        name: `${toolDef.name}Schema`,
        $refStrategy: 'none'
      });

      // Register tool with MCP server
      server.setRequestHandler('tools/call', async (request) => {
        if (request.params.name === toolDef.name) {
          try {
            const result = await toolDef.handler(request.params.arguments || {});
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(result, null, 2)
                }
              ]
            };
          } catch (error) {
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    success: false,
                    error: error.message,
                    tool: toolDef.name,
                    timestamp: new Date().toISOString()
                  }, null, 2)
                }
              ]
            };
          }
        }
      });

      // Store tool definition for tools/list handler
      if (!server._tools) server._tools = [];
      server._tools.push({
        name: toolDef.name,
        description: toolDef.description,
        inputSchema
      });

      totalTools++;
      console.error(`  ✓ Registered tool: ${toolDef.name}`);
    } catch (error) {
      console.error(`  ✗ Failed to register tool ${toolDef.name}:`, error.message);
    }
  }
};

// Load each tool module
toolModules.forEach(modulePath => {
  try {
    const toolModule = require(modulePath);
    if (typeof toolModule === 'function') {
      const toolsCount = totalTools;
      toolModule(mockRpc);
      const newToolsCount = totalTools - toolsCount;
      console.error(`✅ Loaded ${newToolsCount} tools from ${modulePath}`);
    }
  } catch (error) {
    console.error(`⚠️  Could not load ${modulePath}:`, error.message);
  }
});

console.error(`🎉 Successfully loaded ${totalTools} tools total`);

// Add tools/list handler
server.setRequestHandler('tools/list', async () => {
  return {
    tools: server._tools || []
  };
});

console.error('🔗 Starting MCP server...');

// Connect to transport
const transport = new StdioServerTransport();
server.connect(transport);

console.error('✨ MCP Comprehensive Toolkit is ready!');
console.error('🔧 All tools are now available in Claude Desktop!');