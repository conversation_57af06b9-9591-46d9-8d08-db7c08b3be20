require('dotenv').config();
const { AgentRPC } = require('agentrpc');
const { z } = require('zod');

// Initialize AgentRPC
const rpc = new AgentRPC({
  apiSecret: process.env.AGENTRPC_API_SECRET,
});

console.log('🧪 Testing simplified MongoDB tool...');

// Test 1: Simple MongoDB tool with basic schema
rpc.register({
  name: 'testMongoSimple',
  description: 'Simple MongoDB test tool',
  schema: z.object({
    operation: z.enum(['find', 'insertOne']).describe('MongoDB operation'),
    collection: z.string().describe('Collection name')
  }),
  handler: async ({ operation, collection }) => {
    return {
      success: true,
      operation,
      collection,
      timestamp: new Date().toISOString()
    };
  }
});

console.log('✅ Registered simple MongoDB test tool');

// Start listening
rpc.listen();

console.log('🎯 Simple MongoDB test tool is ready!');

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down test server...');
  process.exit(0);
});
