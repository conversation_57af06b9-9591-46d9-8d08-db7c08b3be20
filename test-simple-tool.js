require('dotenv').config();
const { AgentRPC } = require('agentrpc');
const { z } = require('zod');

// Initialize AgentRPC
const rpc = new AgentRPC({
  apiSecret: process.env.AGENTRPC_API_SECRET,
});

console.log('🧪 Testing simple tool registration...');

// Register just one simple test tool
rpc.register({
  name: 'simpleTest',
  description: 'Simple test tool',
  schema: z.object({
    message: z.string().describe('Test message')
  }),
  handler: async ({ message }) => {
    return {
      success: true,
      message,
      timestamp: new Date().toISOString()
    };
  }
});

console.log('✅ Registered simple test tool');
console.log('🔗 Starting AgentRPC server...');

// Start listening
rpc.listen();

console.log('🎯 Simple test tool is ready!');

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down test server...');
  process.exit(0);
});
